spring:
  application:
    name: oskelly-concierge-test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  flyway:
    enabled: false

bitrix:
  url: http://test-bitrix.com
  webhook: test-webhook
  user-id: 1

bitrix-open-line:
  url: http://test-bitrix.com
  webhook: test-webhook
  user-id: 1

monolith:
  host: http://test-monolith.com

logging:
  level:
    root: WARN
    ru.oskelly.concierge: INFO
