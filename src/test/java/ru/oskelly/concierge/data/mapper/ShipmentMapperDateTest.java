package ru.oskelly.concierge.data.mapper;

import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertFalse;

class ShipmentMapperDateTest {

    private final DateTimeFormatter CUSTOM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

    // Копируем логику из ShipmentMapper для тестирования
    private String formatDate(ZonedDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.truncatedTo(ChronoUnit.SECONDS).format(CUSTOM_FORMATTER);
    }

    @Test
    void testFormatDateWithMilliseconds() {
        // Создаем дату с миллисекундами и наносекундами
        ZonedDateTime dateTimeWithMillis = ZonedDateTime.parse("2024-01-15T10:30:45.123456789Z");

        // Форматируем дату
        String formattedDate = formatDate(dateTimeWithMillis);

        // Проверяем, что миллисекунды отсутствуют
        assertFalse(formattedDate.contains(".123"),
                "Formatted date should not contain milliseconds: " + formattedDate);
        assertFalse(formattedDate.contains(".456"),
                "Formatted date should not contain microseconds: " + formattedDate);
        assertFalse(formattedDate.contains(".789"),
                "Formatted date should not contain nanoseconds: " + formattedDate);

        // Проверяем ожидаемый формат
        assertEquals("2024-01-15T10:30:45Z", formattedDate,
                "Date should be formatted without fractional seconds");

        System.out.println("Formatted date with milliseconds: " + formattedDate);
    }

    @Test
    void testFormatDateWithoutMilliseconds() {
        // Создаем дату без миллисекунд
        ZonedDateTime dateTimeWithoutMillis = ZonedDateTime.parse("2024-01-15T10:30:45Z");

        // Форматируем дату
        String formattedDate = formatDate(dateTimeWithoutMillis);

        // Проверяем ожидаемый формат
        assertEquals("2024-01-15T10:30:45Z", formattedDate,
                "Date should be formatted correctly");

        System.out.println("Formatted date without milliseconds: " + formattedDate);
    }

    @Test
    void testFormatDateWithTimezone() {
        // Создаем дату с временной зоной
        ZonedDateTime dateTimeWithTimezone = ZonedDateTime.parse("2024-01-15T10:30:45.123+03:00");

        // Форматируем дату
        String formattedDate = formatDate(dateTimeWithTimezone);

        // Проверяем, что миллисекунды отсутствуют, но временная зона сохранена
        assertFalse(formattedDate.contains(".123"),
                "Formatted date should not contain milliseconds: " + formattedDate);
        assertEquals("2024-01-15T10:30:45+03:00", formattedDate,
                "Date should be formatted without fractional seconds but with timezone");

        System.out.println("Formatted date with timezone: " + formattedDate);
    }

    @Test
    void testFormatNullDate() {
        // Тестируем null дату
        String formattedDate = formatDate(null);

        // Проверяем, что результат null
        assertNull(formattedDate, "Null date should return null");

        System.out.println("Formatted null date: " + formattedDate);
    }
}
