package ru.oskelly.concierge.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class JacksonConfigTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        JacksonConfig jacksonConfig = new JacksonConfig();
        objectMapper = jacksonConfig.objectMapper();
    }

    @Test
    void testZonedDateTimeSerializationWithoutMilliseconds() throws Exception {
        // Создаем дату с миллисекундами и наносекундами
        ZonedDateTime dateTimeWithMillis = ZonedDateTime.parse("2024-01-15T10:30:45.123456789Z");
        
        // Сериализуем в JSON
        String json = objectMapper.writeValueAsString(dateTimeWithMillis);
        
        // Проверяем, что в результате нет миллисекунд
        assertFalse(json.contains(".123"), "JSON should not contain milliseconds: " + json);
        assertFalse(json.contains(".456"), "JSON should not contain microseconds: " + json);
        assertFalse(json.contains(".789"), "JSON should not contain nanoseconds: " + json);
        
        // Проверяем, что дата заканчивается на секунды (без дробной части)
        assertTrue(json.matches("\".*\\d{2}[Z]\"") || json.matches("\".*\\d{2}[+-]\\d{2}:\\d{2}\""),
                "JSON should end with seconds and timezone: " + json);
        
        System.out.println("Serialized date: " + json);
    }

    @Test
    void testZonedDateTimeSerializationAlreadyTruncated() throws Exception {
        // Создаем дату уже обрезанную до секунд
        ZonedDateTime dateTimeWithoutMillis = ZonedDateTime.parse("2024-01-15T10:30:45Z");
        
        // Сериализуем в JSON
        String json = objectMapper.writeValueAsString(dateTimeWithoutMillis);
        
        // Проверяем, что результат корректный
        assertFalse(json.contains("."), "JSON should not contain any fractional seconds: " + json);
        assertTrue(json.matches("\".*\\d{2}[Z]\"") || json.matches("\".*\\d{2}[+-]\\d{2}:\\d{2}\""),
                "JSON should end with seconds and timezone: " + json);
        
        System.out.println("Serialized date (already truncated): " + json);
    }

    @Test
    void testNullZonedDateTimeSerialization() throws Exception {
        ZonedDateTime nullDateTime = null;
        
        String json = objectMapper.writeValueAsString(nullDateTime);
        
        assertTrue(json.equals("null"), "Null date should serialize to 'null': " + json);
        
        System.out.println("Serialized null date: " + json);
    }
}
