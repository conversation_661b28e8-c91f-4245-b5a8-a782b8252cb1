package ru.oskelly.concierge.data.mapper;

import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.controller.dto.ShipmentRequestDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;

import java.util.List;
import java.util.Set;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = OfferMapper.class)
public interface ShipmentMapper {

    @Mapping(target = "purchaseOrderId", source = "purchaseOrder.id")
    @Mapping(target = "createdAt", source = "createdAt")
    ShipmentResponseDTO toResponseDTO(Shipment shipment, @Context PersonalShopperRepository personalShopperRepository);

    @Named("toResponseDTOWithoutOffers")
    @Mapping(target = "offers", ignore = true)
    ShipmentResponseDTO toResponseDTOWithoutOffers(Shipment shipment);

    List<ShipmentResponseDTO> toResponseDtoList(Set<Shipment> shipments, @Context PersonalShopperRepository personalShopperRepository);

    @Mapping(target = "offers", ignore = true)
    @Mapping(target = "creatorId", source = "creatorId")
    @Mapping(target = "purchaseOrder", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "id", ignore = true)
    Shipment toEntityUpdate(ShipmentRequestDTO dto, @MappingTarget Shipment shipment);

    Set<Shipment> toEntityUpdateSet(List<ShipmentRequestDTO> dtos, @Context PurchaseOrder purchaseOrder);

    @AfterMapping
    default void setPurchaseOrder(@MappingTarget Shipment shipment, @Context PurchaseOrder purchaseOrder) {
        shipment.setPurchaseOrder(purchaseOrder);
    }
}
