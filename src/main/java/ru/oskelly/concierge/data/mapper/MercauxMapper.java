package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.controller.mercaux.dto.MercauxPurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.mercaux.dto.MercauxShipmentResponseDTO;

import java.time.ZonedDateTime;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING)
public interface MercauxMapper {

    /**
     * Маппинг от PurchaseOrderFullDTO к MercauxPurchaseOrderFullDTO
     */
    MercauxPurchaseOrderFullDTO toMercauxPurchaseOrderFullDTO(PurchaseOrderFullDTO purchaseOrderFullDTO);

    /**
     * Маппинг от ShipmentResponseDTO к MercauxShipmentResponseDTO
     * Преобразует ZonedDateTime в String для поля createdAt
     */
    @Mapping(target = "createdAt", expression = "java(formatZonedDateTime(shipmentResponseDTO.createdAt()))")
    @Mapping(target = "products", ignore = true)
    @Mapping(target = "buyerOffers", ignore = true)
    MercauxShipmentResponseDTO toMercauxShipmentResponseDTO(ShipmentResponseDTO shipmentResponseDTO);

    /**
     * Вспомогательный метод для форматирования ZonedDateTime в строку
     */
    default String formatZonedDateTime(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        // Используем тот же формат, что и в JacksonConfig
        return zonedDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"));
    }
}
