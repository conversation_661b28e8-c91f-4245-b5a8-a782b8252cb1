package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Данные для заполнения proposedOffer связанным товаром из монолита
 *
 * @param proposedOfferId ID предложения
 * @param productId       ID продукта из монолита
 */
@Schema(description = "DTO для обновления предложения")
public record UpdateProposedOffer(
    @Schema(description = "ID предложения", example = "1")
    Long proposedOfferId,

    @Schema(description = "ID продукта", example = "101")
    Long productId
) {
}
