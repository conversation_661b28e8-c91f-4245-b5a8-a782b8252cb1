package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import lombok.Builder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.TypesSorting;

import java.time.ZonedDateTime;
import java.util.List;

@Builder
public record RequestsFilter(
        @Schema(description = "Список группировок статусов с количеством", type = "array")
        @ArraySchema(schema = @Schema(implementation = PurchaseOrderStatusEnum.class))
        List<PurchaseOrderStatusEnum> orderStatusEnums,

        @Schema(description = "Список группировок источников с количеством", type = "array")
        @ArraySchema(schema = @Schema(implementation = PurchaseOrderSourceEnum.class))
        List<PurchaseOrderSourceEnum> orderSourceEnums,

        @Schema(description = "Список идентификаторов брендов для фильтрации", example = "[1, 2, 3]")
        List<Long> brands,

        @Schema(description = "Список идентификаторов моделей для фильтрации", example = "[101, 102, 103]")
        List<Long> models,

        @Schema(description = "Начальная дата для фильтрации (включительно)", example = "2023-01-01T00:00:00+03:00", format = "date-time")
        ZonedDateTime fromDate,

        @Schema(description = "Идентификатор покупателя", example = "1")
        Long customerId,

        @Schema(description = "Конечная дата для фильтрации (включительно)", example = "2023-12-31T23:59:59+03:00", format = "date-time")
        ZonedDateTime toDate,

        @Schema(description = "Номер страницы для пагинации (начинается с 0)", example = "1", defaultValue = "0", minimum = "0")
        @Min(value = 0, message = "Номер страницы не может быть меньше 0")
        @Valid Long page,

        @Schema(description = "Размер страницы для пагинации", example = "20", defaultValue = "20", minimum = "1")
        @Min(value = 1, message = "Размер страницы не может быть меньше 1")
        @Valid Long pageSize,

        @Schema(description = "Используемый тип сортировки", implementation = TypesSorting.class, defaultValue = "DATE")
        TypesSorting typeSorting,

        @Schema(description = "Возможные типы сортировки",
                implementation = SortingOptionsDto.class)
        SortingOptionsDto possibleSortingTypes

) {
}
