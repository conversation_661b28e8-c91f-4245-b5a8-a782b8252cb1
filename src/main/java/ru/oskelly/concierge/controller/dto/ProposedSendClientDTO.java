package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Set;

/**
 * Содержит идентификатор shipment и набор идентификаторов предложенных офферов.
 */
@Schema(description = "DTO для передачи технической информации")
public record ProposedSendClientDTO(

        @Schema(description = "Идентификатор описания товара", example = "12345")
        Long shipmentId,

        @Schema(description = "Список идентификаторов предложенных офферов")
        @ArraySchema(arraySchema = @Schema(implementation = Long.class))
        Set<Long> proposedOfferIds
) {}
