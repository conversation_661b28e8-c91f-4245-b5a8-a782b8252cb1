package ru.oskelly.concierge.service.component;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.GroupingSourceQuantity;
import ru.oskelly.concierge.controller.dto.GroupingStatusQuantity;
import ru.oskelly.concierge.controller.dto.OrdersForConciergeDTO;
import ru.oskelly.concierge.controller.dto.PaginatedResult;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFilter;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStats;
import ru.oskelly.concierge.controller.dto.RequestsFilter;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.model.QPurchaseOrder;
import ru.oskelly.concierge.data.model.QShipment;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.exception.ValidationException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * _Компонент работы с заявками на покупку_
 */
@Component
@RequiredArgsConstructor
public class DefaultPurchaseOrderComponent implements PurchaseOrderComponent {
    private static final Long DEFAULT_PAGE_SIZE = 20L;
    private static final Long DEFAULT_PAGE_NUMBER = 1L;

    private final JPAQueryFactory queryFactory;
    private final DefaultPurchaseOrderRoleFilterBuilder roleFilterBuilder;

    @Override
    public PurchaseOrderStats getPurchaseOrderStats(PurchaseOrderFilter filter, Long userId, Roles role) {
        QPurchaseOrder purchaseOrder = QPurchaseOrder.purchaseOrder;

        // === Запрос по статусам ===
        JPAQuery<GroupingStatusQuantity> statusQuery = queryFactory
                .select(Projections.constructor(GroupingStatusQuantity.class,
                        purchaseOrder.status,
                        purchaseOrder.count()))
                .from(purchaseOrder)
                .where(purchaseOrder.status.notIn(PurchaseOrderStatusEnum.CANCELLED, PurchaseOrderStatusEnum.REJECTED));

        // === Запрос по источникам ===
        JPAQuery<GroupingSourceQuantity> sourceQuery = queryFactory
                .select(Projections.constructor(GroupingSourceQuantity.class,
                        purchaseOrder.source,
                        purchaseOrder.count()))
                .from(purchaseOrder);

        // === Применение фильтров к обоим запросам ===
        BooleanBuilder whereBuilder = new BooleanBuilder();
        applyRoleFilter(role, whereBuilder, purchaseOrder, userId);
        applyDateFilter(RequestsFilter.builder()
                        .fromDate(filter.fromDate())
                        .toDate(filter.toDate())
                        .build(),
                whereBuilder, purchaseOrder);

        // Добавляем общие условия
        BooleanExpression excludeDraftAndAll = purchaseOrder.status.ne(PurchaseOrderStatusEnum.DRAFT)
                .and(purchaseOrder.status.ne(PurchaseOrderStatusEnum.ALL));

        statusQuery.where(whereBuilder).where(excludeDraftAndAll);
        sourceQuery.where(whereBuilder).where(excludeDraftAndAll);

        // Выполняем оба запроса
        List<GroupingStatusQuantity> statusResult = statusQuery
                .groupBy(purchaseOrder.status)
                .fetch();

        List<GroupingSourceQuantity> sourceResult = sourceQuery
                .groupBy(purchaseOrder.source)
                .fetch();

        // Добавляем "Все" в статусы
        DescriptionStructureEnum allStatusDescription = new DescriptionStructureEnum(
                PurchaseOrderStatusEnum.ALL.name(),
                PurchaseOrderStatusEnum.ALL.getDescription(role),
                PurchaseOrderStatusEnum.ALL.getStatusInfo(role)
        );
        statusResult.add(GroupingStatusQuantity.builder()
                .statusId(allStatusDescription)
                .quantity(statusResult.stream().mapToLong(GroupingStatusQuantity::quantity).sum())
                .description(PurchaseOrderStatusEnum.ALL.getDescription(role))
                .build());

        // Сортируем результаты по порядку enum (используем код для получения enum и его порядок)
        statusResult.sort(Comparator.comparing(item -> {
            try {
                PurchaseOrderStatusEnum enumValue = PurchaseOrderStatusEnum.valueOf(item.statusId().code());
                return enumValue.getOrder();
            } catch (IllegalArgumentException e) {
                return Integer.MAX_VALUE; // неизвестные статусы в конец
            }
        }));
        sourceResult.sort(Comparator.comparing(item -> item.sourceId().name())); // или по description, зависит от нужд

        return new PurchaseOrderStats(statusResult, sourceResult);
    }

    @Override
    public PaginatedResult findAllWithFilter(RequestsFilter filter, Long userId, String searchText, Roles role) {
        QPurchaseOrder purchaseOrder = QPurchaseOrder.purchaseOrder;
        QShipment shipment = QShipment.shipment;

        // === Шаг 1: Создаем базовый запрос для подсчета и выборки ===
        JPAQuery<?> baseQuery = queryFactory.from(purchaseOrder)
                .leftJoin(shipment)
                .on(shipment.purchaseOrder.eq(purchaseOrder));

        // === Шаг 2: Применяем фильтры к whereBuilder ===
        BooleanBuilder whereBuilder = new BooleanBuilder();
        applyRoleFilter(role, whereBuilder, purchaseOrder, userId);
        applyStatusFilter(filter, whereBuilder, purchaseOrder, false);
        applySourceFilter(filter, whereBuilder, purchaseOrder);
        applyDateFilter(filter, whereBuilder, purchaseOrder);
        applyTextSearch(searchText, whereBuilder, purchaseOrder, shipment, role);
        applyCustomerFilter(filter, whereBuilder, purchaseOrder);

        baseQuery
                .where(whereBuilder)
                .groupBy(purchaseOrder.id);

        // === Шаг 3: Получаем общее количество ===
        // Клонируем запрос перед fetchCount, чтобы он не влиял на основной запрос данных
        long totalCount = baseQuery.clone().fetch().size();

        // === Шаг 4: Формируем запрос для выборки данных ===
        JPAQuery<OrdersForConciergeDTO> dataQuery = buildOrdersSearchQuery(purchaseOrder, shipment, whereBuilder);

        // === Шаг 5: Добавляем пагинацию ===
        applyPagination(filter, dataQuery);

        // === Шаг 6: Добавляем сортировку по дате при наличии ===
        if (filter.typeSorting().isDate()) {
            dataQuery.orderBy(purchaseOrder.creationDate.desc());
        }

        // === Шаг 7: Извлекаем данные и добавляем товары к заявкам ===
        List<OrdersForConciergeDTO> fetchData = dataQuery.fetch();
        List<OrdersForConciergeDTO> orders = addShipmentsToOrders(fetchData, role);

        // === Шаг 8: Добавляем сортировку по статусу при наличии ===
        if (filter.typeSorting().isStatus()) {
            sortByStatus(orders);
        }

        long totalPages = getTotalPages(totalCount, getPageSize(filter));

        return new PaginatedResult(orders, totalPages, totalCount);
    }

    private void applyCustomerFilter(RequestsFilter filter, BooleanBuilder whereBuilder, QPurchaseOrder purchaseOrder) {
        if (filter.customerId() != null) {
            whereBuilder.and(purchaseOrder.customerId.eq(filter.customerId()));
        }
    }

    private void applyRoleFilter(Roles role, BooleanBuilder whereBuilder, QPurchaseOrder purchaseOrder, Long userId) {
        whereBuilder.and(
                roleFilterBuilder.build(role, purchaseOrder, userId)
        );
    }

    private void applyStatusFilter(RequestsFilter filter, BooleanBuilder whereBuilder, QPurchaseOrder purchaseOrder, boolean isDisplayDraft) {
        if (filter.orderStatusEnums() != null && !filter.orderStatusEnums().isEmpty()) {
            List<PurchaseOrderStatusEnum> statuses = new ArrayList<>();
            filter.orderStatusEnums().forEach(group -> {
                if (group != null) {
                    statuses.add(group);
                }
            });
            if (!statuses.isEmpty()) {
                whereBuilder.and(purchaseOrder.status.in(statuses));
            }
        }
        if (isDisplayDraft) {
            whereBuilder.and(purchaseOrder.status.ne(PurchaseOrderStatusEnum.DRAFT));
        }
    }

    private void applySourceFilter(RequestsFilter filter, BooleanBuilder whereBuilder, QPurchaseOrder purchaseOrder) {
        if (filter.orderSourceEnums() != null && !filter.orderSourceEnums().isEmpty()) {
            whereBuilder.and(purchaseOrder.source.in(filter.orderSourceEnums()));
        }
    }

    private void applyDateFilter(RequestsFilter filter, BooleanBuilder whereBuilder, QPurchaseOrder purchaseOrder) {
        if (filter.fromDate() != null) {
            whereBuilder.and(purchaseOrder.creationDate.goe(filter.fromDate()));
        }
        if (filter.toDate() != null) {
            whereBuilder.and(purchaseOrder.creationDate.loe(filter.toDate()));
        }
    }

    // Поиск осуществляется по:(для роли sales)
    //  - +ID +
    //  - +Никнейм customer(покупатель)
    //  - +Номер телефона customer(покупатель)
    //  - +Источник
    //  - +Номер заказа +
    // =====================-
    //  Поиск осуществляется по:(для роли sourcer)
    //  - +ID +
    //  - +sales (ФИО + Никнейм)
    //  - +Номер заказа +
    // ToDo :
    // 1. Причесать поиск по постановке выше +
    // 2. В монолите пробрасывать поля сюда для обновление/создания
    // 3. Проверить маппинг полей
    private void applyTextSearch(
            String searchText,
            BooleanBuilder whereBuilder,
            QPurchaseOrder purchaseOrder,
            QShipment shipment,
            Roles role
    ) {
        if (searchText == null) {
            return;
        }

        String trimmedSearch = searchText.trim();

        if (trimmedSearch.isEmpty()) {
            return;
        }

        String searchPattern = "%" + trimmedSearch.toLowerCase() + "%";
        BooleanExpression textSearch;

        // Поиск по атрибутам товаров
        BooleanExpression shipmentSearch = shipment
                .description.lower().like(searchPattern)
                .or(shipment.brandName.lower().like(searchPattern))
                .or(shipment.brandTransliterateName.lower().like(searchPattern))
                .or(shipment.categoryName.lower().like(searchPattern))
                .or(shipment.modelName.lower().like(searchPattern))
                .or(shipment.colorAttributeName.lower().like(searchPattern))
                .or(shipment.purchaseOrder.customerPhone.lower().like(searchPattern));

        // Общие критерии поиска для обеих ролей
        BooleanExpression commonSearch = purchaseOrder
                .id.stringValue().lower().like(searchPattern)
                .or(purchaseOrder.description.lower().like(searchPattern))
                .or(shipmentSearch);

        // Поиск в массиве заказов (список Long)
        BooleanExpression ordersSearch = Expressions.booleanTemplate(
                "CAST({0} AS text) LIKE {1}",
                purchaseOrder.orders,
                searchPattern
        );

        // Специфичные критерии в зависимости от роли
        textSearch = switch (role) {
            case SALES, CONCIERGE_SALES_ADMIN -> commonSearch
                    .or(purchaseOrder.customerNickName.lower().like(searchPattern))
                    .or(purchaseOrder.customerPhone.lower().like(searchPattern))
                    .or(purchaseOrder.source.stringValue().lower().like(searchPattern));

            case SOURCER, CONCIERGE_SOURCERS_ADMIN -> commonSearch
                    .or(purchaseOrder.salesFio.lower().like(searchPattern))
                    .or(purchaseOrder.salesNickName.lower().like(searchPattern));

            default -> commonSearch;
        };

        whereBuilder.and(textSearch.or(ordersSearch));
    }

    private <T> void applyPagination(RequestsFilter filter, JPAQuery<T> query) {
        long pageSize = getPageSize(filter);
        long page = getPageNumber(filter);
        if (page < 1) {
            throw new ValidationException("Номер страницы должен быть больше 0", null, null, null, 400);
        }
        if (pageSize < 1) {
            throw new ValidationException("Размер страницы должен быть больше 0", null, null, null, 400);
        }
        // Уменьшаем на 1, чтобы первая страница (1) соответствовала смещению 0
        long offset = (page - 1) * pageSize;
        query.offset(offset);
        query.limit(pageSize);
    }

    private long getPageSize(RequestsFilter filter) {
        return Objects.requireNonNullElse(filter.pageSize(), DEFAULT_PAGE_SIZE);
    }

    private long getPageNumber(RequestsFilter filter) {
        return Objects.requireNonNullElse(filter.page(), DEFAULT_PAGE_NUMBER);
    }

    private long getTotalPages(long totalCount, long pageSize) {
        return (totalCount + pageSize - 1) / pageSize;
    }

    /**
     * Построение запроса для выборки заявок
     *
     * @param purchaseOrder QueryDSL-сущность заявки
     * @param whereBuilder  построитель условий запроса
     * @return JPAQuery
     */
    private JPAQuery<OrdersForConciergeDTO> buildOrdersSearchQuery(
            QPurchaseOrder purchaseOrder,
            QShipment shipment,
            BooleanBuilder whereBuilder
    ) {
        return queryFactory
                .select(Projections.constructor(OrdersForConciergeDTO.class,
                        purchaseOrder.id,
                        purchaseOrder.description,
                        Projections.constructor(CustomerInfoDTO.class,
                                purchaseOrder.customerId,
                                purchaseOrder.customerNickName,
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                purchaseOrder.customerPhone,
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                Expressions.nullExpression(Boolean.class) // явно указываем тип Boolean
                        ),
                        Projections.constructor(SourcerInfoDTO.class,
                                purchaseOrder.sourcerId,
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                Expressions.nullExpression(String.class), // явно указываем тип String
                                Expressions.nullExpression(String.class) // явно указываем тип String
                        ),
                        Projections.constructor(SalesInfoDTO.class,
                                purchaseOrder.salesId,
                                purchaseOrder.salesFio,
                                purchaseOrder.salesNickName,
                                Expressions.nullExpression(String.class),  // для urlAvatar,
                                Expressions.nullExpression(Roles.class), // для роли Sales
                                Expressions.nullExpression(String.class)
                        ),
                        purchaseOrder.source,
                        purchaseOrder.creationDate,
                        Projections.constructor(DescriptionStructureEnum.class,
                                purchaseOrder.status.stringValue(),
                                purchaseOrder.status.stringValue(),
                                purchaseOrder.status.stringValue()
                        ),
                        purchaseOrder.orders,
                        purchaseOrder.link,
                        purchaseOrder.images
                ))
                .from(purchaseOrder)
                .leftJoin(shipment)
                .on(shipment.purchaseOrder.eq(purchaseOrder))
                .where(whereBuilder)
                .groupBy(purchaseOrder.id);
    }

    /**
     * Добавление товаров в заявки
     *
     * @param orders список найденных заявок
     * @return список заявок с товарами
     */
    private List<OrdersForConciergeDTO> addShipmentsToOrders(List<OrdersForConciergeDTO> orders, Roles role) {
        QShipment shipment = QShipment.shipment;

        // Получение ID всех заказов
        List<Long> orderIds = orders.stream()
                .map(OrdersForConciergeDTO::id)
                .toList();

        // Получение всех связанных shipments
        Map<Long, List<Shipment>> shipmentsByOrderId = queryFactory
                .selectFrom(shipment)
                .where(shipment.purchaseOrder.id.in(orderIds))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(s -> s.getPurchaseOrder().getId()));

        // Заполнение данных о shipments в DTO
        return orders
                .stream()
                .map(dto -> {
                    List<Shipment> orderShipments = shipmentsByOrderId.getOrDefault(dto.id(), Collections.emptyList());

                    List<ShipmentResponseDTO> shipmentDTOs = orderShipments.stream()
                            .map(s -> new ShipmentResponseDTO(
                                    s.getId(),
                                    s.getPurchaseOrder().getId(),
                                    s.getCategoryId(),
                                    s.getCategoryName(),
                                    s.getBrandId(),
                                    s.getBrandTransliterateName(),
                                    s.getMaterialAttributeId(),
                                    s.getMaterialAttributeName(),
                                    s.getColorAttributeId(),
                                    s.getColorAttributeName(),
                                    s.getCreatedAt() != null ? s.getCreatedAt().toString() : null,
                                    s.getModelId(),
                                    s.getModelName(),
                                    s.getShipmentSize(),
                                    s.getDescription(),
                                    s.getImages(),
                                    s.getLinks(),
                                    s.getComment()
                            ))
                            .toList();
                    // Обновляем статус с локализацией
                    PurchaseOrderStatusEnum statusEnum = PurchaseOrderStatusEnum.valueOf(dto.status().code());
                    DescriptionStructureEnum localizedStatus = new DescriptionStructureEnum(
                            dto.status().code(),
                            statusEnum.getDescription(role),
                            statusEnum.getStatusInfo(role)
                    );
                    return dto.withStatus(localizedStatus).withShipments(shipmentDTOs);
                })
                // Специально использовался данный метод, чтобы получить модифицируемую коллекцию для дальнейшей сортировки
                .collect(Collectors.toList());
    }

    /**
     * Если сортировка не по дате, то сортируем по статусу, а внутри статуса - по дате (новые сверху)
     *
     * @param orders список найденных заявок
     */
    private void sortByStatus(List<OrdersForConciergeDTO> orders) {
        orders.sort(
                Comparator
                        .comparing((OrdersForConciergeDTO item) -> {
                            try {
                                PurchaseOrderStatusEnum enumValue = PurchaseOrderStatusEnum.valueOf(item.status().code());
                                return enumValue.getOrder();
                            } catch (IllegalArgumentException e) {
                                return Integer.MAX_VALUE; // неизвестные статусы в конец
                            }
                        })
                        .thenComparing(OrdersForConciergeDTO::creationDate, Comparator.reverseOrder())
        );
    }
}